import pandas as pd

def create_prime_medical_summary():
    """Create a focused summary table for Prime Medical analysis"""
    
    print("=" * 120)
    print("PRIME MEDICAL ANALYSIS - SUMMARY TABLE")
    print("=" * 120)
    print()
    
    # Property data summary
    properties_data = [
        {
            'Property': 'Clinic III Bneid Al Gar',
            'Total_Units': 24,
            'Leased_Units': 18,
            'Available_Units': 6,
            'Total_Monthly_Rent': 46709.00,
            'Prime_Medical_Units': 1,
            'Prime_Medical_Rent': 2680.00,
            'Prime_Customer': 'Dr. <PERSON>',
            'Prime_Unit_Remarks': 'YARROW; (MI/WDC/EW)',
            'Occupancy_Rate': 75.0
        },
        {
            'Property': 'Clinic IV Hawally',
            'Total_Units': 2,
            'Leased_Units': 1,
            'Available_Units': 1,
            'Total_Monthly_Rent': 6600.00,
            'Prime_Medical_Units': 0,
            'Prime_Medical_Rent': 0.00,
            'Prime_Customer': 'None',
            'Prime_Unit_Remarks': 'None',
            'Occupancy_Rate': 50.0
        },
        {
            'Property': 'Clinic V Jabriya',
            'Total_Units': 13,
            'Leased_Units': 12,
            'Available_Units': 0,
            'Total_Monthly_Rent': 58849.00,
            'Prime_Medical_Units': 0,
            'Prime_Medical_Rent': 0.00,
            'Prime_Customer': 'None',
            'Prime_Unit_Remarks': 'None',
            'Occupancy_Rate': 92.3
        },
        {
            'Property': 'Clinic VI-A,B Ras Al Ard, Salmiya (Plot # 37)',
            'Total_Units': 10,
            'Leased_Units': 9,
            'Available_Units': 1,
            'Total_Monthly_Rent': 34750.00,
            'Prime_Medical_Units': 0,
            'Prime_Medical_Rent': 0.00,
            'Prime_Customer': 'None',
            'Prime_Unit_Remarks': 'None',
            'Occupancy_Rate': 90.0
        },
        {
            'Property': 'Clinic VI-C Ras Al Ard, Salmiya (Plot # 38)',
            'Total_Units': 19,
            'Leased_Units': 19,
            'Available_Units': 0,
            'Total_Monthly_Rent': 63175.00,
            'Prime_Medical_Units': 0,
            'Prime_Medical_Rent': 0.00,
            'Prime_Customer': 'None',
            'Prime_Unit_Remarks': 'None',
            'Occupancy_Rate': 100.0
        },
        {
            'Property': 'Clinic VII Jahra',
            'Total_Units': 3,
            'Leased_Units': 3,
            'Available_Units': 0,
            'Total_Monthly_Rent': 12750.00,
            'Prime_Medical_Units': 0,
            'Prime_Medical_Rent': 0.00,
            'Prime_Customer': 'None',
            'Prime_Unit_Remarks': 'None',
            'Occupancy_Rate': 100.0
        },
        {
            'Property': 'Clinic VIII Sabah Al-Salem',
            'Total_Units': 19,
            'Leased_Units': 14,
            'Available_Units': 5,
            'Total_Monthly_Rent': 46800.00,
            'Prime_Medical_Units': 1,
            'Prime_Medical_Rent': 2300.00,
            'Prime_Customer': 'Dr. Mohamad Abdel Kader Al Sayyad',
            'Prime_Unit_Remarks': 'ARAM;',
            'Occupancy_Rate': 73.7
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(properties_data)
    
    # Main summary table
    print("📊 PROPERTY-BY-PROPERTY SUMMARY TABLE")
    print("-" * 120)
    print(f"{'Property':<45} {'Units':<6} {'Leased':<7} {'Avail':<6} {'Occ%':<6} {'Monthly Rent':<12} {'Prime Units':<11} {'Prime Rent':<11}")
    print("-" * 120)
    
    for _, row in df.iterrows():
        property_name = row['Property'][:44]
        total_units = row['Total_Units']
        leased_units = row['Leased_Units']
        available_units = row['Available_Units']
        occupancy = f"{row['Occupancy_Rate']:.1f}%"
        monthly_rent = f"KD {row['Total_Monthly_Rent']:,.0f}"
        prime_units = row['Prime_Medical_Units']
        prime_rent = f"KD {row['Prime_Medical_Rent']:,.0f}" if row['Prime_Medical_Rent'] > 0 else "None"
        
        print(f"{property_name:<45} {total_units:<6} {leased_units:<7} {available_units:<6} {occupancy:<6} {monthly_rent:<12} {prime_units:<11} {prime_rent:<11}")
    
    print("-" * 120)
    
    # Totals
    total_units = df['Total_Units'].sum()
    total_leased = df['Leased_Units'].sum()
    total_available = df['Available_Units'].sum()
    total_rent = df['Total_Monthly_Rent'].sum()
    total_prime_units = df['Prime_Medical_Units'].sum()
    total_prime_rent = df['Prime_Medical_Rent'].sum()
    overall_occupancy = (total_leased / total_units) * 100
    
    print(f"{'PORTFOLIO TOTALS':<45} {total_units:<6} {total_leased:<7} {total_available:<6} {overall_occupancy:.1f}% {'KD ' + f'{total_rent:,.0f}':<12} {total_prime_units:<11} {'KD ' + f'{total_prime_rent:,.0f}':<11}")
    print("-" * 120)
    print()
    
    # Prime Medical Details
    print("🎯 PRIME MEDICAL DETAILED ANALYSIS")
    print("-" * 120)
    
    prime_properties = df[df['Prime_Medical_Units'] > 0]
    
    if len(prime_properties) > 0:
        print(f"{'Property':<45} {'Customer Account':<40} {'Monthly Rent':<12} {'Unit Details':<20}")
        print("-" * 120)
        
        for _, row in prime_properties.iterrows():
            property_name = row['Property'][:44]
            customer = row['Prime_Customer'][:39]
            rent = f"KD {row['Prime_Medical_Rent']:,.2f}"
            details = row['Prime_Unit_Remarks'][:19]
            
            print(f"{property_name:<45} {customer:<40} {rent:<12} {details:<20}")
        
        print("-" * 120)
    
    print()
    
    # Financial Impact Analysis
    print("💰 FINANCIAL IMPACT ANALYSIS")
    print("-" * 120)
    
    prime_percentage = (total_prime_rent / total_rent) * 100
    annual_prime_impact = total_prime_rent * 12
    annual_total_revenue = total_rent * 12
    
    print(f"Total Portfolio Monthly Revenue: KD {total_rent:,.2f}")
    print(f"Total Portfolio Annual Revenue: KD {annual_total_revenue:,.2f}")
    print(f"Prime Medical Monthly Revenue: KD {total_prime_rent:,.2f}")
    print(f"Prime Medical Annual Revenue: KD {annual_prime_impact:,.2f}")
    print(f"Prime Medical Impact on Portfolio: {prime_percentage:.2f}%")
    print()
    
    # Availability Analysis
    print("🏠 UNIT AVAILABILITY ANALYSIS")
    print("-" * 120)
    
    print(f"{'Property':<45} {'Available Units':<15} {'Previous Rent Info':<30}")
    print("-" * 120)
    
    availability_notes = [
        ('Clinic III Bneid Al Gar', '6 units', 'IRIS: KD 2,300 | AL-ASEEL: KD 2,400, 1,850, 2,092, 1,780 | YARROW: KD 2,100'),
        ('Clinic IV Hawally', '1 unit', 'Med Well: KD 5,250'),
        ('Clinic V Jabriya', '0 units', 'All units leased (1 legal hold)'),
        ('Clinic VI-A,B Salmiya (Plot 37)', '1 unit', 'Joya: KD 2,250'),
        ('Clinic VI-C Salmiya (Plot 38)', '0 units', 'Fully occupied'),
        ('Clinic VII Jahra', '0 units', 'Fully occupied'),
        ('Clinic VIII Sabah Al-Salem', '5 units', 'Try Care Clinic units')
    ]
    
    for prop, avail, notes in availability_notes:
        print(f"{prop:<45} {avail:<15} {notes:<30}")
    
    print("-" * 120)
    print()
    
    # Key Insights
    print("🔍 KEY INSIGHTS & RECOMMENDATIONS")
    print("-" * 120)
    
    print("✅ POSITIVE INDICATORS:")
    print("  • High overall occupancy rate: 84.4%")
    print("  • Two properties at 100% occupancy (Clinics VI-C and VII)")
    print("  • Strong revenue performance across all properties")
    print("  • Prime Medical presence confirmed in 2 strategic locations")
    print()
    
    print("⚠️  AREAS FOR ATTENTION:")
    print("  • Clinic IV Hawally: Only 50% occupancy (1 of 2 units)")
    print("  • Clinic VIII: 26.3% availability (5 of 19 units)")
    print("  • 6 available units in Clinic III (25% availability)")
    print()
    
    print("🎯 PRIME MEDICAL STRATEGY:")
    print("  • Current impact: 1.85% of portfolio revenue")
    print("  • Strategic presence in Clinic III (YARROW) and Clinic VIII (ARAM)")
    print("  • Annual revenue contribution: KD 59,760")
    print("  • Both Prime Medical units are actively leased")
    print()
    
    print("📈 RECOMMENDATIONS:")
    print("  1. Focus marketing efforts on filling available units in Clinics III, IV, and VIII")
    print("  2. Review pricing strategy for available units based on previous rent levels")
    print("  3. Monitor Prime Medical unit performance and contract compliance")
    print("  4. Consider expansion opportunities for Prime Medical in other properties")
    print("  5. Analyze reasons for availability in Try Care Clinic units")
    
    print()
    print("=" * 120)
    
    return df

if __name__ == "__main__":
    summary_df = create_prime_medical_summary()
    
    # Save summary to CSV
    summary_df.to_csv('prime_medical_property_summary.csv', index=False)
    print(f"Prime Medical property summary saved to: prime_medical_property_summary.csv")
