import PyPDF2
import pdfplumber
from pathlib import Path

def test_pdf_content(pdf_path):
    """Test PDF content extraction"""
    print(f"Testing PDF: {pdf_path}")
    print("=" * 60)
    
    # Test with pdfplumber
    print("Using pdfplumber:")
    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages[:3]):  # First 3 pages only
                text = page.extract_text()
                if text:
                    print(f"Page {page_num + 1} (first 500 chars):")
                    print(text[:500])
                    print("-" * 40)
                else:
                    print(f"Page {page_num + 1}: No text extracted")
    except Exception as e:
        print(f"pdfplumber error: {e}")
    
    print("\n" + "=" * 60)
    
    # Test with PyPDF2
    print("Using PyPDF2:")
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(reader.pages[:3]):  # First 3 pages only
                text = page.extract_text()
                if text:
                    print(f"Page {page_num + 1} (first 500 chars):")
                    print(text[:500])
                    print("-" * 40)
                else:
                    print(f"Page {page_num + 1}: No text extracted")
    except Exception as e:
        print(f"PyPDF2 error: {e}")

if __name__ == "__main__":
    # Test the Prime Medical contract
    pdf_path = Path(r"C:\Users\<USER>\Documents\Projects\All active Polyclinic Contracts- Sondus\Clinic 8- Subah Alsalim\Management and Operation Contract between Prime Medical & Dr.Mohd. AlSayad, Clinic 8, Floor 4 (A).pdf")
    
    if pdf_path.exists():
        test_pdf_content(pdf_path)
    else:
        print(f"PDF file not found: {pdf_path}")
