from pathlib import Path

def search_filenames(directory, search_terms):
    """Search for terms in PDF filenames"""
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory not found: {directory}")
        return
    
    pdf_files = list(directory_path.glob("**/*.pdf"))
    if not pdf_files:
        print("No PDF files found in directory")
        return
    
    print(f"Searching {len(pdf_files)} PDF filenames for:")
    for term in search_terms:
        print(f"  - '{term}'")
    print("-" * 60)
    
    total_matches = 0
    matches_found = []
    
    for pdf_path in pdf_files:
        relative_path = pdf_path.relative_to(directory_path)
        filename = pdf_path.name.lower()
        
        found_terms = []
        for term in search_terms:
            if term.lower() in filename:
                found_terms.append(term)
                total_matches += 1
        
        if found_terms:
            matches_found.append((relative_path, found_terms))
            print(f"✓ {relative_path}")
            for term in found_terms:
                print(f"    Found: '{term}'")
            print()
    
    print("-" * 60)
    print(f"Total matches: {total_matches}")
    print(f"Files with matches: {len(matches_found)}")
    
    if matches_found:
        print("\nSummary of matching files:")
        for i, (file_path, terms) in enumerate(matches_found, 1):
            print(f"{i}. {file_path}")
            print(f"   Terms found: {', '.join(terms)}")

if __name__ == "__main__":
    directory = r"C:\Users\<USER>\Documents\Projects\All active Polyclinic Contracts- Sondus"
    search_terms = [
        # Arabic terms
        "مديكال برايم", 
        "برايم مديكال", 
        "ميد سيل برايم",
        # English terms
        "Prime Medical",
        "Medical Prime", 
        "Med Cell Prime",
        "Prime",
        "Medical"
    ]
    
    search_filenames(directory, search_terms)
