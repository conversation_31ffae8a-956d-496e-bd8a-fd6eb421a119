import PyPDF2
import pdfplumber
import os
import glob
from pathlib import Path

def list_pdf_files(directory):
    """List all PDF files in directory"""
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory not found: {directory}")
        return []
    
    pdf_files = list(directory_path.glob("*.pdf"))
    if not pdf_files:
        print("No PDF files found in directory")
        return []
    
    print(f"Found {len(pdf_files)} PDF files:")
    print("-" * 60)
    for i, pdf_path in enumerate(pdf_files, 1):
        file_size = pdf_path.stat().st_size / (1024 * 1024)  # Size in MB
        print(f"{i:2d}. {pdf_path.name} ({file_size:.1f} MB)")
    print("-" * 60)
    return pdf_files

def search_with_pypdf2(pdf_path, search_terms):
    """Search using PyPDF2 library"""
    results = {}
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(reader.pages):
                text = page.extract_text()
                for term in search_terms:
                    if term in text:
                        if term not in results:
                            results[term] = []
                        results[term].append(page_num + 1)
    except Exception as e:
        print(f"PyPDF2 error in {pdf_path}: {e}")
    return results

def search_with_pdfplumber(pdf_path, search_terms):
    """Search using pdfplumber library (better text extraction)"""
    results = {}
    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                text = page.extract_text()
                if text:
                    for term in search_terms:
                        if term in text:
                            if term not in results:
                                results[term] = []
                            results[term].append(page_num + 1)
    except Exception as e:
        print(f"pdfplumber error in {pdf_path}: {e}")
    return results

def search_pdf_directory(directory, search_terms):
    """Search all PDF files in directory"""
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory not found: {directory}")
        return
    
    pdf_files = list(directory_path.glob("*.pdf"))
    if not pdf_files:
        print("No PDF files found in directory")
        return
    
    print(f"Searching {len(pdf_files)} PDF files for:")
    for term in search_terms:
        print(f"  - '{term}'")
    print("-" * 60)
    
    total_matches = 0
    
    for pdf_path in pdf_files:
        print(f"Searching: {pdf_path.name}")
        
        # Try pdfplumber first (better text extraction)
        results = search_with_pdfplumber(pdf_path, search_terms)
        
        # If no results, try PyPDF2 as backup
        if not results:
            results = search_with_pypdf2(pdf_path, search_terms)
        
        if results:
            for term, pages in results.items():
                total_matches += len(pages)
                print(f"  ✓ '{term}' found on pages: {', '.join(map(str, sorted(set(pages))))}")
        else:
            print(f"  - Not found")
    
    print("-" * 60)
    print(f"Total matches: {total_matches}")

# Main execution
if __name__ == "__main__":
    directory = r"C:\Users\<USER>\Documents\Projects\All active Polyclinic Contracts- Sondus"
    search_terms = ["مديكال برايم", "برايم مديكال", "شركة ميد سيل برايم لدراسة وتنفيذ وإدارة المشاريع الصحية (ش.ش.و)"]
    
    # List files first
    pdf_files = list_pdf_files(directory)
    
    if pdf_files:
        print()  # Empty line
        search_pdf_directory(directory, search_terms) 
