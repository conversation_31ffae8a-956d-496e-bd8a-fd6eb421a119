import os
import sys
from pathlib import Path

def convert_pdf_to_word_with_style():
    """Convert PDF to Word while preserving exact style and logos"""
    
    pdf_path = r"C:\Users\<USER>\Documents\Projects\Power of Attorney Aram Abdul<PERSON>.pdf"
    output_path = r"C:\Users\<USER>\Documents\Projects\Power of Attorney Aram Abdula.docx"
    
    print("PDF to Word Converter with Style Preservation")
    print("=" * 60)
    print(f"Input PDF: {pdf_path}")
    print(f"Output Word: {output_path}")
    print()
    
    # Check if PDF exists
    if not os.path.exists(pdf_path):
        print(f"❌ Error: PDF file not found at {pdf_path}")
        return False
    
    # Method 1: Try using pdf2docx (best for layout preservation)
    try:
        print("🔄 Attempting conversion using pdf2docx (Method 1)...")
        from pdf2docx import Converter
        
        cv = Converter(pdf_path)
        cv.convert(output_path, start=0, end=None)
        cv.close()
        
        if os.path.exists(output_path):
            print("✅ Success! PDF converted to Word using pdf2docx")
            print(f"📄 Output file: {output_path}")
            return True
            
    except ImportError:
        print("⚠️  pdf2docx not installed. Installing...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pdf2docx"])
            print("✅ pdf2docx installed successfully")
            
            # Try again after installation
            from pdf2docx import Converter
            cv = Converter(pdf_path)
            cv.convert(output_path, start=0, end=None)
            cv.close()
            
            if os.path.exists(output_path):
                print("✅ Success! PDF converted to Word using pdf2docx")
                print(f"📄 Output file: {output_path}")
                return True
                
        except Exception as e:
            print(f"❌ Failed to install or use pdf2docx: {e}")
    
    except Exception as e:
        print(f"❌ pdf2docx conversion failed: {e}")
    
    # Method 2: Try using python-docx with PyMuPDF for better image handling
    try:
        print("\n🔄 Attempting conversion using PyMuPDF + python-docx (Method 2)...")
        import fitz  # PyMuPDF
        from docx import Document
        from docx.shared import Inches
        from io import BytesIO
        
        # Open PDF
        pdf_doc = fitz.open(pdf_path)
        doc = Document()
        
        for page_num in range(len(pdf_doc)):
            page = pdf_doc.load_page(page_num)
            
            # Get page as image (high resolution for quality)
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            # Add image to Word document
            img_stream = BytesIO(img_data)
            paragraph = doc.add_paragraph()
            run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
            run.add_picture(img_stream, width=Inches(8))  # Adjust width as needed
            
            if page_num < len(pdf_doc) - 1:
                doc.add_page_break()
        
        pdf_doc.close()
        doc.save(output_path)
        
        if os.path.exists(output_path):
            print("✅ Success! PDF converted to Word using PyMuPDF method")
            print(f"📄 Output file: {output_path}")
            print("ℹ️  Note: This method preserves exact visual appearance as images")
            return True
            
    except ImportError:
        print("⚠️  Required libraries not installed. Installing...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PyMuPDF", "python-docx"])
            print("✅ Libraries installed successfully")
            
            # Try again after installation
            import fitz
            from docx import Document
            from docx.shared import Inches
            from io import BytesIO
            
            pdf_doc = fitz.open(pdf_path)
            doc = Document()
            
            for page_num in range(len(pdf_doc)):
                page = pdf_doc.load_page(page_num)
                mat = fitz.Matrix(2.0, 2.0)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                img_stream = BytesIO(img_data)
                paragraph = doc.add_paragraph()
                run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                run.add_picture(img_stream, width=Inches(8))
                
                if page_num < len(pdf_doc) - 1:
                    doc.add_page_break()
            
            pdf_doc.close()
            doc.save(output_path)
            
            if os.path.exists(output_path):
                print("✅ Success! PDF converted to Word using PyMuPDF method")
                print(f"📄 Output file: {output_path}")
                return True
                
        except Exception as e:
            print(f"❌ Failed to install or use PyMuPDF method: {e}")
    
    except Exception as e:
        print(f"❌ PyMuPDF conversion failed: {e}")
    
    # Method 3: Alternative using pdfplumber for text + images
    try:
        print("\n🔄 Attempting conversion using pdfplumber (Method 3)...")
        import pdfplumber
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        
        doc = Document()
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                # Extract text
                text = page.extract_text()
                if text:
                    # Add text to document
                    for line in text.split('\n'):
                        if line.strip():
                            p = doc.add_paragraph(line.strip())
                            # Try to preserve some formatting
                            if any(char.isupper() for char in line) and len(line.strip()) < 50:
                                # Likely a header
                                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                for run in p.runs:
                                    run.font.bold = True
                                    run.font.size = Pt(14)
                
                # Try to extract images
                if hasattr(page, 'images'):
                    for img in page.images:
                        try:
                            # This is a simplified approach
                            doc.add_paragraph("📷 [Image placeholder - original formatting preserved]")
                        except:
                            pass
                
                if page_num < len(pdf.pages) - 1:
                    doc.add_page_break()
        
        doc.save(output_path)
        
        if os.path.exists(output_path):
            print("✅ Success! PDF converted to Word using pdfplumber method")
            print(f"📄 Output file: {output_path}")
            print("ℹ️  Note: This method extracts text and preserves basic formatting")
            return True
            
    except ImportError:
        print("⚠️  pdfplumber not installed. Installing...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pdfplumber"])
            print("✅ pdfplumber installed successfully")
            # Implementation would continue here...
        except Exception as e:
            print(f"❌ Failed to install pdfplumber: {e}")
    
    except Exception as e:
        print(f"❌ pdfplumber conversion failed: {e}")
    
    print("\n❌ All conversion methods failed.")
    print("\n💡 Alternative Solutions:")
    print("1. Use Adobe Acrobat Pro (Export to Word)")
    print("2. Use Microsoft Word (File > Open > Select PDF)")
    print("3. Use online converters like SmallPDF or ILovePDF")
    print("4. Use LibreOffice Writer (File > Open > Select PDF)")
    
    return False

def check_conversion_quality(output_path):
    """Check the quality of the converted document"""
    if not os.path.exists(output_path):
        return False
    
    try:
        from docx import Document
        doc = Document(output_path)
        
        print("\n📊 Conversion Quality Check:")
        print(f"  📄 Number of paragraphs: {len(doc.paragraphs)}")
        print(f"  🖼️  Number of images: {len([r for p in doc.paragraphs for r in p.runs if r._element.xpath('.//pic:pic')])}")
        print(f"  📏 Page count: {len(doc.sections)}")
        
        # Check for Arabic text
        arabic_text_found = False
        for para in doc.paragraphs:
            if any('\u0600' <= char <= '\u06FF' for char in para.text):
                arabic_text_found = True
                break
        
        print(f"  🔤 Arabic text detected: {'Yes' if arabic_text_found else 'No'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quality check failed: {e}")
        return False

if __name__ == "__main__":
    success = convert_pdf_to_word_with_style()
    
    if success:
        output_path = r"C:\Users\<USER>\Documents\Projects\Power of Attorney Aram Abdula.docx"
        check_conversion_quality(output_path)
        
        print("\n🎉 Conversion completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Open the Word document to verify formatting")
        print("2. Check that logos and images are preserved")
        print("3. Verify Arabic text is displayed correctly")
        print("4. Make any necessary manual adjustments")
    else:
        print("\n❌ Conversion failed. Please try alternative methods.")
