import PyPDF2
import pdfplumber
import os
import glob
from pathlib import Path

# OCR libraries
try:
    import pytesseract
    import pdf2image
    from PIL import Image
    OCR_AVAILABLE = True
    print("OCR libraries available - will use OCR for scanned PDFs")
except ImportError:
    OCR_AVAILABLE = False
    print("OCR libraries not available - install with: pip install pytesseract pdf2image pillow")
    print("Also need to install Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki")

def list_pdf_files(directory):
    """List all PDF files in directory and subdirectories"""
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory not found: {directory}")
        return []
    
    pdf_files = list(directory_path.glob("**/*.pdf"))  # Recursive search
    if not pdf_files:
        print("No PDF files found in directory")
        return []
    
    print(f"Found {len(pdf_files)} PDF files:")
    print("-" * 60)
    for i, pdf_path in enumerate(pdf_files, 1):
        file_size = pdf_path.stat().st_size / (1024 * 1024)  # Size in MB
        relative_path = pdf_path.relative_to(directory_path)
        print(f"{i:2d}. {relative_path} ({file_size:.1f} MB)")
    print("-" * 60)
    return pdf_files

def search_with_pypdf2(pdf_path, search_terms):
    """Search using PyPDF2 library"""
    results = {}
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(reader.pages):
                text = page.extract_text()
                if text and text.strip():  # Check if text is not empty
                    for term in search_terms:
                        if term.lower() in text.lower():
                            if term not in results:
                                results[term] = []
                            results[term].append(page_num + 1)
    except Exception as e:
        print(f"PyPDF2 error in {pdf_path}: {e}")
    return results

def search_with_pdfplumber(pdf_path, search_terms):
    """Search using pdfplumber library (better text extraction)"""
    results = {}
    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                text = page.extract_text()
                if text and text.strip():  # Check if text is not empty
                    for term in search_terms:
                        if term.lower() in text.lower():
                            if term not in results:
                                results[term] = []
                            results[term].append(page_num + 1)
    except Exception as e:
        print(f"pdfplumber error in {pdf_path}: {e}")
    return results

def search_with_ocr(pdf_path, search_terms, max_pages=5):
    """Search using OCR for scanned PDFs"""
    if not OCR_AVAILABLE:
        return {}
    
    results = {}
    try:
        # Convert PDF to images
        images = pdf2image.convert_from_path(pdf_path, first_page=1, last_page=max_pages)
        
        for page_num, image in enumerate(images):
            # Perform OCR on the image
            text = pytesseract.image_to_string(image, lang='ara+eng')  # Arabic + English
            
            if text and text.strip():
                for term in search_terms:
                    if term.lower() in text.lower():
                        if term not in results:
                            results[term] = []
                        results[term].append(page_num + 1)
                        
    except Exception as e:
        print(f"OCR error in {pdf_path}: {e}")
    return results

def search_pdf_directory(directory, search_terms):
    """Search all PDF files in directory and subdirectories"""
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory not found: {directory}")
        return
    
    pdf_files = list(directory_path.glob("**/*.pdf"))  # Recursive search
    if not pdf_files:
        print("No PDF files found in directory")
        return
    
    print(f"Searching {len(pdf_files)} PDF files for:")
    for term in search_terms:
        print(f"  - '{term}'")
    print("-" * 60)
    
    total_matches = 0
    
    for pdf_path in pdf_files:
        relative_path = pdf_path.relative_to(directory_path)
        print(f"Searching: {relative_path}")
        
        # Try pdfplumber first (better text extraction)
        results = search_with_pdfplumber(pdf_path, search_terms)
        
        # If no results, try PyPDF2 as backup
        if not results:
            results = search_with_pypdf2(pdf_path, search_terms)
        
        # If still no results and OCR is available, try OCR
        if not results and OCR_AVAILABLE:
            print(f"  Trying OCR...")
            results = search_with_ocr(pdf_path, search_terms)
        
        if results:
            for term, pages in results.items():
                total_matches += len(pages)
                print(f"  ✓ '{term}' found on pages: {', '.join(map(str, sorted(set(pages))))}")
        else:
            print(f"  - Not found")
    
    print("-" * 60)
    print(f"Total matches: {total_matches}")

# Main execution
if __name__ == "__main__":
    directory = r"C:\Users\<USER>\Documents\Projects\All active Polyclinic Contracts- Sondus"
    search_terms = [
        # Arabic terms
        "مديكال برايم", 
        "برايم مديكال", 
        "شركة ميد سيل برايم لدراسة وتنفيذ وإدارة المشاريع الصحية (ش.ش.و)",
        "ميد سيل برايم",
        # English terms
        "Prime Medical",
        "Medical Prime", 
        "Med Cell Prime",
        "Prime",
        "Medical"
    ]
    
    # List files first
    pdf_files = list_pdf_files(directory)
    
    if pdf_files:
        print()  # Empty line
        search_pdf_directory(directory, search_terms) 
