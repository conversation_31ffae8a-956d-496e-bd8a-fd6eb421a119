from pathlib import Path

def create_simple_table(directory):
    """Create a simple table showing all PDF files and their status"""
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory not found: {directory}")
        return
    
    pdf_files = list(directory_path.glob("**/*.pdf"))
    if not pdf_files:
        print("No PDF files found in directory")
        return
    
    # Target company name and variations
    target_company = "شركة برايم ميديكال لدراسة وتنفيذ وادارة المشاريع الصحية"
    search_terms = [
        "شركة برايم ميديكال لدراسة وتنفيذ وادارة المشاريع الصحية",
        "برايم ميديكال",
        "شركة برايم ميديكال",
        "Prime Medical"
    ]
    
    print("=" * 120)
    print(f"CONTRACT STATUS TABLE")
    print(f"Target Company: {target_company}")
    print("=" * 120)
    
    changed_count = 0
    not_changed_count = 0
    
    # Sort files alphabetically
    pdf_files.sort(key=lambda x: str(x))
    
    for i, pdf_path in enumerate(pdf_files, 1):
        relative_path = pdf_path.relative_to(directory_path)
        filename = pdf_path.name.lower()
        
        # Check if any search terms are found
        found_terms = []
        for term in search_terms:
            if term.lower() in filename:
                found_terms.append(term)
        
        # Determine status
        if found_terms:
            status = "CHANGED"
            changed_count += 1
        else:
            status = "NOT CHANGED"
            not_changed_count += 1
        
        print(f"{i:2d}. [{status:11}] {relative_path}")
    
    print("=" * 120)
    print(f"SUMMARY:")
    print(f"Total Files: {len(pdf_files)}")
    print(f"CHANGED (Contains company name): {changed_count}")
    print(f"NOT CHANGED (Does not contain company name): {not_changed_count}")
    print("=" * 120)

if __name__ == "__main__":
    directory = r"C:\Users\<USER>\Documents\Projects\All active Polyclinic Contracts- Sondus"
    create_simple_table(directory)
