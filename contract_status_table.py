from pathlib import Path
import pandas as pd

def create_contract_status_table(directory):
    """Create a table showing all PDF files and their status regarding the target company name"""
    directory_path = Path(directory)
    if not directory_path.exists():
        print(f"Directory not found: {directory}")
        return
    
    pdf_files = list(directory_path.glob("**/*.pdf"))
    if not pdf_files:
        print("No PDF files found in directory")
        return
    
    # Target company name and variations
    target_company = "شركة برايم ميديكال لدراسة وتنفيذ وادارة المشاريع الصحية"
    search_terms = [
        "شركة برايم ميديكال لدراسة وتنفيذ وادارة المشاريع الصحية",
        "برايم ميديكال",
        "شركة برايم ميديكال",
        "Prime Medical"
    ]
    
    # Prepare data for table
    table_data = []
    
    for pdf_path in pdf_files:
        relative_path = pdf_path.relative_to(directory_path)
        filename = pdf_path.name.lower()
        
        # Check if any search terms are found
        found_terms = []
        for term in search_terms:
            if term.lower() in filename:
                found_terms.append(term)
        
        # Determine status
        if found_terms:
            status = "CHANGED"
            matched_terms = ", ".join(found_terms)
        else:
            status = "NOT CHANGED"
            matched_terms = "None"
        
        table_data.append({
            "File Name": str(relative_path),
            "Status": status,
            "Matched Terms": matched_terms
        })
    
    # Create DataFrame
    df = pd.DataFrame(table_data)
    
    # Sort by status (CHANGED first) then by file name
    df = df.sort_values(['Status', 'File Name'], ascending=[False, True])
    
    # Display summary
    total_files = len(df)
    changed_files = len(df[df['Status'] == 'CHANGED'])
    not_changed_files = len(df[df['Status'] == 'NOT CHANGED'])
    
    print(f"CONTRACT STATUS ANALYSIS")
    print(f"Target Company: {target_company}")
    print("=" * 80)
    print(f"Total Files: {total_files}")
    print(f"Changed (Contains company name): {changed_files}")
    print(f"Not Changed (Does not contain company name): {not_changed_files}")
    print("=" * 80)
    print()
    
    # Display table
    print("DETAILED TABLE:")
    print("-" * 120)
    
    # Print header
    print(f"{'No.':<4} {'Status':<12} {'File Name':<80} {'Matched Terms':<20}")
    print("-" * 120)
    
    # Print rows
    for idx, row in df.iterrows():
        file_name = row['File Name']
        if len(file_name) > 75:
            file_name = file_name[:72] + "..."
        
        matched_terms = row['Matched Terms']
        if len(matched_terms) > 18:
            matched_terms = matched_terms[:15] + "..."
            
        print(f"{idx+1:<4} {row['Status']:<12} {file_name:<80} {matched_terms:<20}")
    
    print("-" * 120)
    
    # Save to CSV file
    csv_filename = "contract_status_report.csv"
    df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
    print(f"\nReport saved to: {csv_filename}")
    
    return df

if __name__ == "__main__":
    directory = r"C:\Users\<USER>\Documents\Projects\All active Polyclinic Contracts- Sondus"
    df = create_contract_status_table(directory)
