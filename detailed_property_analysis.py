import pandas as pd
import numpy as np

def analyze_detailed_property_data():
    """Analyze detailed polyclinic data with customer accounts by property"""
    
    # Raw data with customer accounts
    data_text = """Customer Account	Property	Unit Status	Monthly Rent	Unit Remarks	Status of changing name to Prime
<PERSON> Alhajri	Clinic  III Bneid Al Gar	Leased	2,100.00	IRIS; (WDC/EW)	
Smart Health Co., for Medical Machines & Supplies	Clinic  III Bneid Al Gar	Leased	2,100.00	IRIS; (WDC/EW)	
Walid Hamad Ashwi Raheel	Clinic  III Bneid Al Gar	Leased	1,282.00	IRIS; (WDC/EW)	
Dr. <PERSON><PERSON>utb	Clinic  III Bneid Al Gar	Leased	4,900.00	IRIS; (MI/WDC/EW)	
Dr. <PERSON><PERSON><PERSON>	Clinic  III Bneid Al Gar	Leased	2,200.00	IRIS; (MI/WDC/EW);	
Hassan A <PERSON>aber & Obaid Metni Khazzaz Almutairi	Clinic  III Bneid Al Gar	Leased	1,976.00	IRIS; (MI/WDC/EW)	
	Clinic  III Bneid Al Gar	Available	0	IRIS; (MI/WDC/EW); Prev. Rent: KD 2,300	
Dr. <PERSON><PERSON>Turki	Clinic  III Bneid Al Gar	Leased	1,866.00	IRIS; (MI/WDC/EW)	
Skills United General Trading Co. (Reem Osama Kanawati)	Clinic  III Bneid Al Gar	Leased	4,800.00	AL-ASEEL; (MI/WDC/EW)	
	Clinic  III Bneid Al Gar	Available	0	AL-ASEEL; (MI/WDC/EW); Prev. Rent: KD 2,400	
Dr. Abdullah Abdulrahman Al-Hassan	Clinic  III Bneid Al Gar	Leased	2,200.00	AL-ASEEL; (MI/WDC/EW)	
Hossam Mohamed El Badry Ibrahim	Clinic  III Bneid Al Gar	Leased	2,175.00	AL-ASEEL; (MI/WDC/EW)	
	Clinic  III Bneid Al Gar	Available	0	AL-ASEEL; (EW); Prev. Rent: KD 1,850	
Nasser Faisal Nasser Al-Mutairi	Clinic  III Bneid Al Gar	Leased	2,300.00	AL-ASEEL; (MI/WDC/EW)	
Andro George Michael Gras	Clinic  III Bneid Al Gar	Leased	2,200.00	AL-ASEEL; (MI/WDC/EW);	
	Clinic  III Bneid Al Gar	Available	0	AL-ASEEL; (MI/WDC/EW); Prev. Rent: KD 2,092	
Dr. Ali Al-Mukaimi & Nisreen Marwan Baeij	Clinic  III Bneid Al Gar	Leased	4,000.00	AL-ASEEL; (MI/WDC/EW);	
	Clinic  III Bneid Al Gar	Available	0	AL-ASEEL; (MI/WDC/EW); Prev. Rent: KD 1,780	
Dr. Ahmed Abdulsamad Yehya Jassem	Clinic  III Bneid Al Gar	Leased	2,700.00	YARROW; (MI/WDC/EW)	
	Clinic  III Bneid Al Gar	Available	0	YARROW; (MI/WDC/EW); Prev Rent 2100	
Hossam Mohamed El Badry Ibrahim	Clinic  III Bneid Al Gar	Leased	2,400.00	YARROW; (MI/WDC/EW)	
Dr. Ahmed Mohamed Ahmed Ibrahim	Clinic  III Bneid Al Gar	Leased	2,680.00	YARROW; (MI/WDC/EW)	changed
Eng. Saleh Abdul Ghaffar Mansour Marafi	Clinic  III Bneid Al Gar	Leased	2,618.00	YARROW; (MI/WDC/EW)	
Adnan Ibrahim Ibrahim	Clinic  III Bneid Al Gar	Leased	2,212.00	YARROW; (MI/WDC/EW)	
Dr. Sulaiman A M Hajji & Dr. Khaled Yousef Khaled Aljenaei	Clinic  IV Hawally	Leased	6,600.00	Med Well Polyclinic; EW	
	Clinic  IV Hawally	Available	0	Med Well Polyclinic; (EW); Prev. Rent: KD 5,250	
Dr. Salwa Mahmoud Mandani Haidar & Dr. Abdulaziz Fahad Abdulaziz Al-Muzaini	Clinic  V Jabriya	Leased	5,000.00	Fourth Medical Center; (MI/WDC/EW)	
Dr. Salam Attar	Clinic  V Jabriya	Leased	3,000.00	Fourth Medical Center; (MI/WDC/EW)	
One Day to Manage and Follow up Projects Co.	Clinic  V Jabriya	Leased	7,000.00	Fourth Medical Center; (MI/WDC/EW)	
Athba Co., for Management of Specialized Medical Centers	Clinic  V Jabriya	Leased	6,400.00	Fourth Medical Center; (MI/WDC/EW)	
Health Care Co., for Management of Specialized Medical Centers	Clinic  V Jabriya	Leased	3,200.00	Fourth Medical Center; (MI/WDC/EW)	
Dr. Abdulaziz Fahad Abdulaziz Al-Muzaini	Clinic  V Jabriya	Leased	2,400.00	Fourth Medical Center; (MI/WDC/EW)	
Gulf Comprehensive Company for Specialized Medical Centers Management	Clinic  V Jabriya	Legal Hold - Inactive	12,000.00	Fourth Medical Center; (MI/WDC)	
Revolution Medical Group	Clinic  V Jabriya	Leased	2,475.00	Fourth Medical Center; (MI/WDC)	
Dr. Farouk Alzoubani	Clinic  V Jabriya	Leased	3,000.00	Fourth Medical Center; (MI/WDC)	
Assem Drwesh Mostafa Abdulnabi	Clinic  V Jabriya	Leased	3,000.00	Fourth Medical Center; (MI/WDC)	
One Day to Manage and Follow up Projects Co.	Clinic  V Jabriya	Leased	3,000.00	Fourth Medical Center; (MI/WDC)	
Dr. Abdullah Sadad Sabri Al-Ozairi	Clinic  V Jabriya	Leased	1,334.00	Fourth Medical Center; (MI/WDC)	
Gulf Care General Trading Co.	Clinic  V Jabriya	Leased	7,040.00	Fourth Medical Center; (MI/WDC)	
Med Marine Company for Private Medical Laboratory Management	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	3,900.00	Med-Marine; (MI/WDC)	
Meshal Khaled Abdullah Aldahash	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	4,700.00	Med-Marine; (MI/WDC);	
Dr. Fatemah Neama Ghloum Zaid Al-Awadi	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	2,400.00	Med-Marine; (MI/WDC)	
Dr. Mohamed Asaad Shehadeh Eid & Dr. Wael Bizra	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	2,050.00	Med-Marine; (MI/WDC)	
Mohamed Youssef Mohamed Al-Sabti	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	4,450.00	Med-Marine; (MI/WDC)	
Dr. Mohamed Asaad Shehadeh Eid & Dr. Wael Bizra	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	4,950.00	Med-Marine; (MI/WDC);	
Dr. Ihab Mohammed Younus Omar	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	2,600.00	Joya; (WDC)	
Mohamed Mustafa Riyad Ali Saleh	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Available	0	Joya; (WDC) Prev Rent 2250	
Mohamed Mustafa Shehta Zurub	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	4,850.00	Joya; (WDC)	
Dr. Shehta Mostafa Shehta Zurub	Clinic  VI-A,B Ras Al Ard, Salmiya (Plot # 37)	Leased	4,850.00	Joya; (WDC)	
Moayyad Zaid Mohammad Alsaqoubi	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	2,500.00	1C-OT - Medical Harbour; (MI/WDC)	
Dr. Mohamed Abdelmageed Mahmoud Hassan	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	2,750.00	2C-OT - Medical Harbour; (MI/WDC)	
Dr. Salah El-Din Mohamed Fathi Elsherbieny	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,000.00	3C-OT - Medical Harbour; (MI/WDC)	
Dr. Youssef Abdullah Ibrahim Al-Khulaifi & Rawan Sulaiman Al-Khatib	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,000.00	Medical Harbour; (MI/WDC)	
Dr. Hanouf B. J. Alsomait	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	8,250.00	Medical Harbour; (MI/WDC)	
Amr Essa Attia Khala	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,100.00	Medical Harbour; (MI/WDC)	
Dr. Hesham Mohamed Yassin Ibrahim	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,100.00	Medical Harbour; (MI/WDC)	
Med Vision Medical Services Company	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	2,975.00	Medical Harbour; (MI/WDC)	
Fatma Mohamed Fakhreldin Mohamed Badawi	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,000.00	Medical Harbour; (MI/WDC)	
Othman Yousef Mansour Al-Masoud	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,100.00	Medical Harbour; (MI/WDC)	
Btissam Ben Kirane	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,000.00	Medical Harbour; (MI/WDC)	
Meshal Khaled Abdullah Aldahash	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,100.00	Medical Harbour; (MI/WDC)	
Dr. Amal Mohamed Abdel Wahab Al-Shaiji & Dr. Faisal Youssef Abdel Rahman Al-Tarkit	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,150.00	Medical Harbour; (MI/WDC)	
Dr. Mustapha M. Tomsu	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,500.00	Medical Harbour; (MI/WDC)	
Walid Hamad Ashwi Raheel	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,150.00	Medical Harbour; (MI/WDC)	
Dr. Eman Mokhtar Mokhtar Ghorab	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,000.00	Medical Harbour; (MI/WDC)	
Emad S A Morcus, Dr. Ahmed O Youssef, Mario S R Biqtar + Specialist Nadia H Mannai	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,150.00	Medical Harbour; (MI/WDC)	
Dr. Mohammed Ibrahim M Al-Kaluk & Wael Sabry Ali Al-Sayed	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,150.00	Medical Harbour; (MI/WDC)	
Dr. Youssef Abdullah Ibrahim Al-Khulaifi	Clinic  VI-C Ras Al Ard, Salmiya (Plot # 38)	Leased	3,200.00	Medical Harbour; (MI/WDC)	
Med Gray Company for Private Medical Laboratory Management	Clinic  VII Jahra	Leased	2,750.00	Med-Gray; (MI/WDC)	
Dr. Amr Nabil Abdellalil Qutb	Clinic  VII Jahra	Leased	3,000.00	Med-Gray; (MI/WDC)	
Dr. Shehta Mostafa Shehta Zurub	Clinic  VII Jahra	Leased	7,000.00	Med-Gray; (MI/WDC)	
Rwda Ahmed Mohammed Omar	Clinic  VIII Sabah Al-Salem	Leased	2,300.00	ARAM;	
Lamiaa A H Elmoselhy & Dalia Mohamed Abdelhamid Attia	Clinic  VIII Sabah Al-Salem	Leased	2,300.00	ARAM;	
Dalia Mohamed Abdelhamid Attia, Mina Yousef Fouad Salama, Osama Mohamed Tawfik Youssef, Mahmoud Refaat Mohamed Saad	Clinic  VIII Sabah Al-Salem	Leased	4,600.00	ARAM;	
Ayman Fawzi Abdelmoteib Badaei & Islam Wahid Atif Fathy Mahmoud	Clinic  VIII Sabah Al-Salem	Leased	5,700.00	ARAM;	
Dr. Mohamad Abdel Kader Al Sayyad	Clinic  VIII Sabah Al-Salem	Leased	2,300.00	ARAM;	changed
Dr. Mohamad Abdel Kader Al Sayyad	Clinic  VIII Sabah Al-Salem	Leased	2,300.00	ARAM;	
Beshoy B B Abdelmalak, Mina Yousef F Salama, Zaher Audi	Clinic  VIII Sabah Al-Salem	Leased	2,300.00	ARAM;	
Naser Falah Owaidhah Alajmi & Mohammad Jassim Mohammad Ali	Clinic  VIII Sabah Al-Salem	Leased	2,300.00	ARAM;	
Munirah A E Alhajeri & Enjoud Hamad Sulaiman Alghanim	Clinic  VIII Sabah Al-Salem	Leased	6,000.00	ARAM;	
Dr. Sondos Ghanim Al Khaledi	Clinic  VIII Sabah Al-Salem	Leased	4,700.00	ARAM;	
Marina Ibrahim Kamal Shaker, Mary Samir Abdelshaheed Girgis & Mariana Mohsen Milad Halim	Clinic  VIII Sabah Al-Salem	Leased	3,000.00	ARAM;	
Dr. Mohammed Sami Ahmed Ibrahim Salem	Clinic  VIII Sabah Al-Salem	Leased	3,000.00	ARAM;	
Rwda Ahmed Mohammed Omar	Clinic  VIII Sabah Al-Salem	Leased	3,000.00	ARAM;	
Marawan Essam Elsayed Hussein	Clinic  VIII Sabah Al-Salem	Leased	3,000.00	ARAM;	
	Clinic  VIII Sabah Al-Salem	Available	0	Try care clinic	
	Clinic  VIII Sabah Al-Salem	Available	0	Try care clinic	
	Clinic  VIII Sabah Al-Salem	Available	0	Try care clinic	
	Clinic  VIII Sabah Al-Salem	Available	0	Try care clinic	
	Clinic  VIII Sabah Al-Salem	Available	0	Try care clinic	
	Clinic  VIII Sabah Al-Salem	Available	0	Try care clinic"""
    
    # Parse the data
    lines = data_text.strip().split('\n')
    headers = lines[0].split('\t')
    
    data = []
    for i, line in enumerate(lines[1:], 1):
        parts = line.split('\t')
        if len(parts) >= 6:
            customer_account = parts[0].strip()
            property_name = parts[1].strip()
            unit_status = parts[2].strip()
            monthly_rent_str = parts[3].strip().replace(',', '')
            unit_remarks = parts[4].strip()
            prime_status = parts[5].strip() if len(parts) > 5 else ""
            
            # Convert rent to float
            try:
                monthly_rent = float(monthly_rent_str) if monthly_rent_str and monthly_rent_str != '0' else 0.0
            except:
                monthly_rent = 0.0
            
            # Determine Prime Medical status
            prime_medical_status = "CHANGED" if prime_status.lower() == "changed" else "NOT CHANGED"
            
            data.append({
                'Unit_ID': i,
                'Customer_Account': customer_account,
                'Property': property_name,
                'Unit_Status': unit_status,
                'Monthly_Rent': monthly_rent,
                'Unit_Remarks': unit_remarks,
                'Prime_Status_Original': prime_status,
                'Prime_Medical_Status': prime_medical_status
            })
    
    df = pd.DataFrame(data)
    return df

def generate_property_analysis_report(df):
    """Generate comprehensive property-by-property analysis"""
    
    print("=" * 120)
    print("DETAILED PROPERTY-BY-PROPERTY ANALYSIS REPORT")
    print("=" * 120)
    print()
    
    # Group by property
    properties = df['Property'].unique()
    
    for property_name in properties:
        property_data = df[df['Property'] == property_name]
        
        print(f"🏥 PROPERTY: {property_name}")
        print("=" * 100)
        
        # Property summary statistics
        total_units = len(property_data)
        leased_units = len(property_data[property_data['Unit_Status'] == 'Leased'])
        available_units = len(property_data[property_data['Unit_Status'] == 'Available'])
        legal_hold_units = len(property_data[property_data['Unit_Status'] == 'Legal Hold - Inactive'])
        
        total_monthly_rent = property_data['Monthly_Rent'].sum()
        active_monthly_rent = property_data[property_data['Unit_Status'] == 'Leased']['Monthly_Rent'].sum()
        
        # Prime Medical analysis
        prime_changed = len(property_data[property_data['Prime_Medical_Status'] == 'CHANGED'])
        prime_changed_rent = property_data[property_data['Prime_Medical_Status'] == 'CHANGED']['Monthly_Rent'].sum()
        
        print(f"📊 SUMMARY STATISTICS:")
        print(f"  Total Units: {total_units}")
        print(f"  Leased Units: {leased_units} ({leased_units/total_units*100:.1f}%)")
        print(f"  Available Units: {available_units} ({available_units/total_units*100:.1f}%)")
        if legal_hold_units > 0:
            print(f"  Legal Hold Units: {legal_hold_units} ({legal_hold_units/total_units*100:.1f}%)")
        print()
        
        print(f"💰 FINANCIAL ANALYSIS:")
        print(f"  Total Monthly Rent: KD {total_monthly_rent:,.2f}")
        print(f"  Active Monthly Rent (Leased): KD {active_monthly_rent:,.2f}")
        print(f"  Annual Revenue Potential: KD {active_monthly_rent * 12:,.2f}")
        if prime_changed > 0:
            print(f"  Prime Medical Monthly Rent: KD {prime_changed_rent:,.2f}")
            print(f"  Prime Medical Annual Impact: KD {prime_changed_rent * 12:,.2f}")
        print()
        
        print(f"🎯 PRIME MEDICAL STATUS:")
        print(f"  Units Changed to Prime: {prime_changed}")
        print(f"  Units Not Changed: {total_units - prime_changed}")
        if prime_changed > 0:
            print(f"  Prime Medical Impact: {prime_changed_rent/total_monthly_rent*100:.1f}% of total rent")
        print()
        
        # Detailed unit breakdown
        print(f"📋 DETAILED UNIT BREAKDOWN:")
        print(f"{'#':<3} {'Customer Account':<50} {'Status':<12} {'Rent (KD)':<10} {'Prime':<8} {'Unit Remarks':<30}")
        print("-" * 120)
        
        for idx, row in property_data.iterrows():
            unit_num = str(idx + 1)
            customer = row['Customer_Account'][:49] if row['Customer_Account'] else "N/A"
            status = row['Unit_Status'][:11]
            rent = f"{row['Monthly_Rent']:,.0f}"
            prime_status = "CHANGED" if row['Prime_Medical_Status'] == 'CHANGED' else "NO"
            remarks = row['Unit_Remarks'][:29]
            
            print(f"{unit_num:<3} {customer:<50} {status:<12} {rent:<10} {prime_status:<8} {remarks:<30}")
        
        print("-" * 120)
        print()
        print()
    
    # Overall portfolio summary
    print("🌟 OVERALL PORTFOLIO SUMMARY")
    print("=" * 100)
    
    total_portfolio_rent = df['Monthly_Rent'].sum()
    total_prime_rent = df[df['Prime_Medical_Status'] == 'CHANGED']['Monthly_Rent'].sum()
    total_units_all = len(df)
    total_prime_units = len(df[df['Prime_Medical_Status'] == 'CHANGED'])
    
    print(f"📈 PORTFOLIO TOTALS:")
    print(f"  Total Units Across All Properties: {total_units_all}")
    print(f"  Total Monthly Rent: KD {total_portfolio_rent:,.2f}")
    print(f"  Total Annual Revenue: KD {total_portfolio_rent * 12:,.2f}")
    print()
    
    print(f"🎯 PRIME MEDICAL PORTFOLIO IMPACT:")
    print(f"  Total Prime Medical Units: {total_prime_units}")
    print(f"  Prime Medical Monthly Rent: KD {total_prime_rent:,.2f}")
    print(f"  Prime Medical Annual Impact: KD {total_prime_rent * 12:,.2f}")
    print(f"  Percentage of Portfolio: {total_prime_rent/total_portfolio_rent*100:.2f}%")
    print()
    
    # Property ranking by revenue
    property_revenue = df.groupby('Property')['Monthly_Rent'].sum().sort_values(ascending=False)
    
    print(f"🏆 PROPERTY RANKING BY MONTHLY REVENUE:")
    for i, (prop, revenue) in enumerate(property_revenue.items(), 1):
        print(f"  {i}. {prop}: KD {revenue:,.2f}")
    
    print()
    print("=" * 120)
    
    return df

if __name__ == "__main__":
    # Analyze the detailed data
    df = analyze_detailed_property_data()
    
    # Generate comprehensive property analysis
    analysis_df = generate_property_analysis_report(df)
    
    # Save detailed data to CSV
    df.to_csv('detailed_property_analysis.csv', index=False)
    print(f"Detailed property analysis saved to: detailed_property_analysis.csv")
